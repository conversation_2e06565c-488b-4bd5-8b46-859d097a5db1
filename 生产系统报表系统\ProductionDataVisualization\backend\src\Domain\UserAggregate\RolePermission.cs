using System;
using Domain.Common;

namespace Domain.UserAggregate
{
    /// <summary>
    /// 角色-权限关联实体
    /// </summary>
    public class RolePermission : Entity
    {
        public Guid RoleId { get; private set; }
        public Guid PermissionId { get; private set; }

        public Role Role { get; private set; }
        public Permission Permission { get; private set; }

        // 防止无参构造函数被外部调用
        private RolePermission() { }

        public RolePermission(Role role, Permission permission)
        {
            if (role == null)
                throw new ArgumentNullException(nameof(role));
            
            if (permission == null)
                throw new ArgumentNullException(nameof(permission));

            RoleId = role.Id;
            PermissionId = permission.Id;
            Role = role;
            Permission = permission;
        }
    }
}