using Domain.DataAggregate;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Infrastructure.Persistence.Configurations
{
    /// <summary>
    /// 数据类别实体配置
    /// </summary>
    public class DataCategoryConfiguration : IEntityTypeConfiguration<DataCategory>
    {
        public void Configure(EntityTypeBuilder<DataCategory> builder)
        {
            builder.ToTable("DataCategories");

            builder.HasKey(dc => dc.Id);

            builder.Property(dc => dc.Name)
                .IsRequired()
                .HasMaxLength(100);

            builder.Property(dc => dc.Description)
                .HasMaxLength(500);

            builder.Property(dc => dc.Unit)
                .HasMaxLength(50);

            builder.Property(dc => dc.IsActive)
                .IsRequired();

            builder.Property(dc => dc.CreatedAt)
                .IsRequired();

            builder.Property(dc => dc.ModifiedAt);

            builder.Property(dc => dc.CreatedBy)
                .HasMaxLength(50);

            builder.Property(dc => dc.ModifiedBy)
                .HasMaxLength(50);

            // 索引
            builder.HasIndex(dc => dc.Name)
                .IsUnique();
        }
    }
} 