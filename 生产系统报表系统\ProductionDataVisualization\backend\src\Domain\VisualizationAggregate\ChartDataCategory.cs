using System;
using Domain.Common;
using Domain.DataAggregate;

namespace Domain.VisualizationAggregate
{
    /// <summary>
    /// 图表-数据类别关联实体
    /// </summary>
    public class ChartDataCategory : Entity
    {
        public Guid ChartId { get; private set; }
        public Guid DataCategoryId { get; private set; }

        public Chart Chart { get; private set; }
        public DataCategory DataCategory { get; private set; }

        // 防止无参构造函数被外部调用
        private ChartDataCategory() { }

        public ChartDataCategory(Chart chart, DataCategory dataCategory)
        {
            if (chart == null)
                throw new ArgumentNullException(nameof(chart));
            
            if (dataCategory == null)
                throw new ArgumentNullException(nameof(dataCategory));

            ChartId = chart.Id;
            DataCategoryId = dataCategory.Id;
            Chart = chart;
            DataCategory = dataCategory;
        }
    }
} 