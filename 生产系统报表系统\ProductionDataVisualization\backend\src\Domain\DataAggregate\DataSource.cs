using System;
using System.Collections.Generic;
using Domain.Common;

namespace Domain.DataAggregate
{
    /// <summary>
    /// 数据源实体，作为聚合根
    /// </summary>
    public class DataSource : Entity, IAggregateRoot
    {
        public string Name { get; private set; }
        public string Description { get; private set; }
        public string SourceType { get; private set; } // TXT, XLSX, CSV
        public string FilePath { get; private set; }
        public DateTime ImportedAt { get; private set; }
        public Guid ImportedBy { get; private set; }
        public int TotalRows { get; private set; }
        public bool IsProcessed { get; private set; }
        public string? ProcessingError { get; private set; }

        private readonly List<DataPoint> _dataPoints = new();
        public IReadOnlyCollection<DataPoint> DataPoints => _dataPoints.AsReadOnly();

        // 防止无参构造函数被外部调用
        private DataSource() { }

        public DataSource(
            string name, 
            string description, 
            string sourceType, 
            string filePath, 
            Guid importedBy)
        {
            if (string.IsNullOrWhiteSpace(name))
                throw new ArgumentException("数据源名称不能为空", nameof(name));

            if (string.IsNullOrWhiteSpace(sourceType))
                throw new ArgumentException("数据源类型不能为空", nameof(sourceType));

            if (string.IsNullOrWhiteSpace(filePath))
                throw new ArgumentException("文件路径不能为空", nameof(filePath));

            Name = name;
            Description = description ?? string.Empty;
            SourceType = sourceType;
            FilePath = filePath;
            ImportedAt = DateTime.UtcNow;
            ImportedBy = importedBy;
            TotalRows = 0;
            IsProcessed = false;
        }

        public void MarkAsProcessed(int totalRows)
        {
            IsProcessed = true;
            TotalRows = totalRows;
            ProcessingError = null;
            ModifiedAt = DateTime.UtcNow;
        }

        public void MarkAsError(string error)
        {
            IsProcessed = true;
            ProcessingError = error;
            ModifiedAt = DateTime.UtcNow;
        }

        public void Update(string name, string description)
        {
            if (!string.IsNullOrWhiteSpace(name))
                Name = name;

            if (description != null)
                Description = description;

            ModifiedAt = DateTime.UtcNow;
        }

        public void AddDataPoint(DataPoint dataPoint)
        {
            if (dataPoint == null)
                throw new ArgumentNullException(nameof(dataPoint));

            _dataPoints.Add(dataPoint);
        }

        public void AddDataPoints(IEnumerable<DataPoint> dataPoints)
        {
            if (dataPoints == null)
                throw new ArgumentNullException(nameof(dataPoints));

            foreach (var dataPoint in dataPoints)
            {
                AddDataPoint(dataPoint);
            }
        }
    }
} 