# 生产数据可视化系统 - 启动问题解决方案

## 问题描述

在某些Windows系统环境中，由于PowerShell执行策略的限制，系统可能无法正常启动。主要表现为：

1. 无法执行npm命令
2. PowerShell脚本（如start_ps.ps1）无法运行
3. 即使使用批处理文件（如start.bat），其中涉及的npm命令仍可能失败

## 解决方案

我们提供了三种解决方案，请按顺序尝试：

### 方案一：使用解决PowerShell限制工具

1. 找到`ProductionDataVisualization/scripts/解决PowerShell限制.bat`
2. 双击运行该批处理文件
3. 在弹出的管理员权限PowerShell窗口中，系统会自动设置执行策略为RemoteSigned
4. 关闭该PowerShell窗口
5. 尝试运行`快速启动.bat`启动系统

### 方案二：使用纯CMD模式启动

如果方案一未能解决问题，请尝试：

1. 找到`ProductionDataVisualization/scripts/cmd启动.bat`
2. 双击运行该批处理文件
3. 该脚本将使用纯CMD命令启动系统，避开PowerShell

### 方案三：手动解决PowerShell执行策略

1. 按Win+X，选择"Windows PowerShell (管理员)"
2. 在打开的PowerShell窗口中执行以下命令：
   ```powershell
   Set-ExecutionPolicy -Scope CurrentUser -ExecutionPolicy RemoteSigned
   ```
3. 输入"Y"确认更改
4. 关闭PowerShell窗口
5. 尝试重新运行`start.bat`或`快速启动.bat`

## 查看详细错误信息

如果系统仍然无法启动，请运行诊断工具并将结果保存：

1. 找到`ProductionDataVisualization/scripts/diagnose.bat`
2. 双击运行
3. 将输出结果截图或复制保存
4. 将结果提供给技术支持人员

## 其他常见问题

### 端口被占用

如果3000或5000端口被其他程序占用，您可能需要修改端口：

1. 在`ProductionDataVisualization/scripts`文件夹中找到相关启动脚本
2. 使用文本编辑器打开
3. 修改端口号（默认前端3000，后端5000）
4. 保存修改并重新启动系统

### 安装依赖失败

如果npm install命令失败：

1. 检查网络连接
2. 尝试使用管理员权限运行命令提示符
3. 手动执行以下命令：
   ```cmd
   cd 到前端目录路径
   npm install --no-audit --no-fund
   ```

如需进一步帮助，请参阅`错误排查指南.md`文档或联系系统管理员。 