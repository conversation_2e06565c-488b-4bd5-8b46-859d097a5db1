using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Domain.Common;
using Domain.DataAggregate;
using Microsoft.EntityFrameworkCore;

namespace Infrastructure.Persistence.Repositories
{
    /// <summary>
    /// 数据类别仓储实现
    /// </summary>
    public class DataCategoryRepository : BaseRepository<DataCategory>, IDataCategoryRepository
    {
        public DataCategoryRepository(ApplicationDbContext dbContext) : base(dbContext)
        {
        }

        public async Task<IEnumerable<DataCategory>> GetByDataSourceIdAsync(Guid dataSourceId)
        {
            return await DbContext.DataCategories
                .Where(dc => dc.DataSourceId == dataSourceId)
                .ToListAsync();
        }

        public async Task<DataCategory?> GetByNameAndSourceIdAsync(string name, Guid dataSourceId)
        {
            return await DbContext.DataCategories
                .FirstOrDefaultAsync(dc => dc.Name == name && dc.DataSourceId == dataSourceId);
        }

        public async Task<IEnumerable<DataCategory>> GetByIdsAsync(IEnumerable<Guid> ids)
        {
            return await DbContext.DataCategories
                .Where(dc => ids.Contains(dc.Id))
                .Include(dc => dc.DataSource)
                .Include(dc => dc.DataPoints)
                .Include(dc => dc.Thresholds)
                .ToListAsync();
        }
    }
} 