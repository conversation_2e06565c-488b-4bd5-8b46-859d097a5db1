<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>React直接测试</title>
  <script src="https://unpkg.com/react@18/umd/react.development.js"></script>
  <script src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
  <!-- 使用jwt-decode的UMD版本 -->
  <script src="https://unpkg.com/jwt-decode@4.0.0/build/jwt-decode.umd.js"></script>
  <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
  <style>
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
      margin: 0;
      padding: 20px;
      background-color: #f0f2f5;
    }
    .container {
      max-width: 800px;
      margin: 0 auto;
      background-color: white;
      padding: 20px;
      border-radius: 8px;
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    }
    .button {
      background-color: #1890ff;
      color: white;
      border: none;
      padding: 10px 20px;
      border-radius: 4px;
      cursor: pointer;
      font-size: 16px;
      margin-top: 20px;
    }
    .button:hover {
      background-color: #40a9ff;
    }
    .success {
      color: green;
      padding: 10px;
      background-color: #f6ffed;
      border: 1px solid #b7eb8f;
      border-radius: 4px;
      margin: 10px 0;
    }
    .error {
      color: red;
      padding: 10px;
      background-color: #fff2f0;
      border: 1px solid #ffccc7;
      border-radius: 4px;
      margin: 10px 0;
    }
    .result {
      margin-top: 20px;
      padding: 10px;
      background-color: #f5f5f5;
      border-radius: 4px;
      white-space: pre-wrap;
    }
  </style>
</head>
<body>
  <div class="container">
    <h1>React和JWT-Decode直接测试</h1>
    <div id="root"></div>
  </div>

  <script type="text/babel">
    // 测试组件
    function TestApp() {
      const [jwtResult, setJwtResult] = React.useState(null);
      const [jwtError, setJwtError] = React.useState(null);
      const [renderResult, setRenderResult] = React.useState(null);
      
      // 测试JWT解码
      const testJwtDecode = () => {
        try {
          // 清除之前的结果
          setJwtResult(null);
          setJwtError(null);
          
          // 创建一个测试token
          const testToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkFkbWluIFVzZXIiLCJpYXQiOjE1MTYyMzkwMjIsImV4cCI6MTkxNjIzOTAyMn0.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c';
          
          // 尝试解码，使用jwtDecode函数
          const decoded = jwtDecode(testToken);
          
          // 显示结果
          setJwtResult(decoded);
        } catch (error) {
          console.error('JWT解码失败:', error);
          setJwtError(error.message || '未知错误');
        }
      };
      
      // 测试对象渲染
      const testObjectRendering = () => {
        try {
          // 创建一个测试对象
          const testObj = { name: 'test', value: 123 };
          
          // 正确渲染对象
          setRenderResult(testObj);
        } catch (error) {
          console.error('渲染测试失败:', error);
          alert(`渲染测试失败: ${error.message}`);
        }
      };
      
      return (
        <div>
          <h2>功能测试</h2>
          
          <div>
            <button className="button" onClick={testJwtDecode}>测试JWT解码</button>
            {jwtError && <div className="error">错误: {jwtError}</div>}
            {jwtResult && (
              <div className="success">
                <p>解码成功!</p>
                <div className="result">{JSON.stringify(jwtResult, null, 2)}</div>
              </div>
            )}
          </div>
          
          <hr />
          
          <div>
            <button className="button" onClick={testObjectRendering}>测试对象渲染</button>
            {renderResult && (
              <div className="success">
                <p>对象属性渲染成功:</p>
                <p>名称: {renderResult.name}</p>
                <p>值: {renderResult.value}</p>
              </div>
            )}
          </div>
          
          <hr />
          
          <div>
            <h3>React信息</h3>
            <p><strong>React版本:</strong> {React.version}</p>
            <p><strong>当前时间:</strong> {new Date().toLocaleString()}</p>
          </div>
        </div>
      );
    }

    // 确保DOM已完全加载后再渲染
    document.addEventListener('DOMContentLoaded', function() {
      const rootElement = document.getElementById('root');
      if (rootElement) {
        const root = ReactDOM.createRoot(rootElement);
        root.render(<TestApp />);
      } else {
        console.error('找不到ID为root的DOM元素');
      }
    });
  </script>
</body>
</html> 