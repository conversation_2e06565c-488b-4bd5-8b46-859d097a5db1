using Domain.DataAggregate;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Infrastructure.Persistence.Configurations
{
    /// <summary>
    /// 阈值实体配置
    /// </summary>
    public class ThresholdConfiguration : IEntityTypeConfiguration<Threshold>
    {
        public void Configure(EntityTypeBuilder<Threshold> builder)
        {
            builder.ToTable("Thresholds");

            builder.HasKey(t => t.Id);

            builder.Property(t => t.DataCategoryId)
                .IsRequired();

            builder.Property(t => t.Name)
                .IsRequired()
                .HasMaxLength(100);

            builder.Property(t => t.Description)
                .HasMaxLength(500);

            builder.Property(t => t.MinValue)
                .IsRequired();

            builder.Property(t => t.MaxValue)
                .IsRequired();

            builder.Property(t => t.Color)
                .IsRequired()
                .HasMaxLength(20);

            builder.Property(t => t.IsActive)
                .IsRequired();

            builder.Property(t => t.CreatedAt)
                .IsRequired();

            builder.Property(t => t.ModifiedAt);

            builder.Property(t => t.CreatedBy)
                .HasMaxLength(50);

            builder.Property(t => t.ModifiedBy)
                .HasMaxLength(50);

            // 关系
            builder.HasOne(t => t.DataCategory)
                .WithMany(dc => dc.Thresholds)
                .HasForeignKey(t => t.DataCategoryId)
                .OnDelete(DeleteBehavior.Cascade);

            // 索引
            builder.HasIndex(t => t.DataCategoryId);
            builder.HasIndex(t => t.IsActive);
        }
    }
} 