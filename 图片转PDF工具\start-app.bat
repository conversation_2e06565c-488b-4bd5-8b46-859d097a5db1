@echo off
chcp 65001 >nul
title 图片转PDF工具 - 启动应用
echo ========================================
echo   图片转PDF工具 - License集成版本
echo ========================================
echo.

echo [1] 启动License验证服务器...
cd server
start "License Server" cmd /k "npm start"
cd ..

echo [2] 等待服务器启动...
timeout /t 3 /nobreak >nul

echo [3] 启动前端开发服务器...
start "Frontend Dev" cmd /k "npm run dev"

echo [4] 等待前端启动...
timeout /t 5 /nobreak >nul

echo [5] 打开浏览器...
start http://localhost:5173

echo.
echo ========================================
echo           启动完成！
echo ========================================
echo.
echo 🌐 访问地址:
echo    前端应用: http://localhost:5173
echo    API服务: http://localhost:3001
echo.
echo 🔐 测试授权码:
echo    有效: TEST_LICENSE_CODE_12345
echo    过期: EXPIRED_LICENSE_CODE_12345
echo.
echo 📋 使用说明:
echo    1. 点击右上角"未授权"按钮
echo    2. 复制硬件指纹
echo    3. 输入测试授权码
echo    4. 验证成功后即可使用
echo.
echo 按任意键关闭此窗口...
pause >nul
