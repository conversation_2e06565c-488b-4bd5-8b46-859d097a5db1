using Domain.UserAggregate;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Infrastructure.Persistence.Configurations
{
    /// <summary>
    /// 权限实体配置
    /// </summary>
    public class PermissionConfiguration : IEntityTypeConfiguration<Permission>
    {
        public void Configure(EntityTypeBuilder<Permission> builder)
        {
            builder.HasKey(p => p.Id);

            builder.Property(p => p.Name)
                .IsRequired()
                .HasMaxLength(50);

            builder.Property(p => p.Description)
                .HasMaxLength(200);

            builder.Property(p => p.Code)
                .IsRequired()
                .HasMaxLength(50);

            builder.Property(p => p.CreatedAt)
                .IsRequired();

            builder.Property(p => p.ModifiedAt)
                .IsRequired(false);

            // 索引
            builder.HasIndex(p => p.Name).IsUnique();
            builder.HasIndex(p => p.Code).IsUnique();

            // 预定义权限数据
            builder.HasData(
                new Permission(Permission.ManageUsersPermissionId, "管理用户", "可以创建、编辑和删除用户", "users.manage"),
                new Permission(Permission.ImportDataPermissionId, "导入数据", "可以导入数据源文件", "data.import"),
                new Permission(Permission.ViewDashboardsPermissionId, "查看仪表盘", "可以查看所有仪表盘", "dashboards.view"),
                new Permission(Permission.ManageChartsPermissionId, "管理图表", "可以创建、编辑和删除图表", "charts.manage"),
                new Permission(Permission.ManageSystemPermissionId, "管理系统", "可以管理系统设置", "system.manage")
            );
        }
    }
} 