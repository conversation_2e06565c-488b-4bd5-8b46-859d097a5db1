# 生产数据可视化系统 - 数据库结构设计文档

## 1. 概述

本文档描述了生产数据可视化系统的数据库结构设计，包括表结构、字段定义、索引设计和关系模型。

## 2. 数据库表结构

### 2.1 用户领域表

#### Users（用户表）

| 列名 | 数据类型 | 约束 | 描述 |
|------|----------|------|------|
| Id | uniqueidentifier | PK | 主键 |
| Username | nvarchar(50) | NOT NULL, UQ | 用户名，唯一 |
| Email | nvarchar(100) | NOT NULL, UQ | 邮箱，唯一 |
| PasswordHash | nvarchar(200) | NOT NULL | 密码哈希 |
| FullName | nvarchar(100) | NOT NULL | 全名 |
| IsActive | bit | NOT NULL | 是否激活 |
| LastLoginTime | datetime2 | NULL | 最后登录时间 |
| CreatedAt | datetime2 | NOT NULL | 创建时间 |
| ModifiedAt | datetime2 | NULL | 修改时间 |
| CreatedBy | nvarchar(50) | NULL | 创建人 |
| ModifiedBy | nvarchar(50) | NULL | 修改人 |

**索引**：
- PK_Users (Id)
- UQ_Users_Username (Username)
- UQ_Users_Email (Email)

#### Roles（角色表）

| 列名 | 数据类型 | 约束 | 描述 |
|------|----------|------|------|
| Id | uniqueidentifier | PK | 主键 |
| Name | nvarchar(50) | NOT NULL, UQ | 角色名称，唯一 |
| Description | nvarchar(200) | NULL | 角色描述 |
| CreatedAt | datetime2 | NOT NULL | 创建时间 |
| ModifiedAt | datetime2 | NULL | 修改时间 |
| CreatedBy | nvarchar(50) | NULL | 创建人 |
| ModifiedBy | nvarchar(50) | NULL | 修改人 |

**索引**：
- PK_Roles (Id)
- UQ_Roles_Name (Name)

#### Permissions（权限表）

| 列名 | 数据类型 | 约束 | 描述 |
|------|----------|------|------|
| Id | uniqueidentifier | PK | 主键 |
| Name | nvarchar(50) | NOT NULL | 权限名称 |
| Description | nvarchar(200) | NULL | 权限描述 |
| Code | nvarchar(50) | NOT NULL, UQ | 权限代码，唯一 |
| CreatedAt | datetime2 | NOT NULL | 创建时间 |
| ModifiedAt | datetime2 | NULL | 修改时间 |
| CreatedBy | nvarchar(50) | NULL | 创建人 |
| ModifiedBy | nvarchar(50) | NULL | 修改人 |

**索引**：
- PK_Permissions (Id)
- UQ_Permissions_Code (Code)

#### UserRoles（用户-角色关联表）

| 列名 | 数据类型 | 约束 | 描述 |
|------|----------|------|------|
| Id | uniqueidentifier | PK | 主键 |
| UserId | uniqueidentifier | NOT NULL, FK | 用户ID，外键 |
| RoleId | uniqueidentifier | NOT NULL, FK | 角色ID，外键 |
| CreatedAt | datetime2 | NOT NULL | 创建时间 |
| ModifiedAt | datetime2 | NULL | 修改时间 |
| CreatedBy | nvarchar(50) | NULL | 创建人 |
| ModifiedBy | nvarchar(50) | NULL | 修改人 |

**索引**：
- PK_UserRoles (Id)
- UQ_UserRoles_UserId_RoleId (UserId, RoleId)
- IX_UserRoles_UserId (UserId)
- IX_UserRoles_RoleId (RoleId)

**外键**：
- FK_UserRoles_Users (UserId) REFERENCES Users(Id)
- FK_UserRoles_Roles (RoleId) REFERENCES Roles(Id)

#### RolePermissions（角色-权限关联表）

| 列名 | 数据类型 | 约束 | 描述 |
|------|----------|------|------|
| Id | uniqueidentifier | PK | 主键 |
| RoleId | uniqueidentifier | NOT NULL, FK | 角色ID，外键 |
| PermissionId | uniqueidentifier | NOT NULL, FK | 权限ID，外键 |
| CreatedAt | datetime2 | NOT NULL | 创建时间 |
| ModifiedAt | datetime2 | NULL | 修改时间 |
| CreatedBy | nvarchar(50) | NULL | 创建人 |
| ModifiedBy | nvarchar(50) | NULL | 修改人 |

**索引**：
- PK_RolePermissions (Id)
- UQ_RolePermissions_RoleId_PermissionId (RoleId, PermissionId)
- IX_RolePermissions_RoleId (RoleId)
- IX_RolePermissions_PermissionId (PermissionId)

**外键**：
- FK_RolePermissions_Roles (RoleId) REFERENCES Roles(Id)
- FK_RolePermissions_Permissions (PermissionId) REFERENCES Permissions(Id)

### 2.2 数据领域表

#### DataSources（数据源表）

| 列名 | 数据类型 | 约束 | 描述 |
|------|----------|------|------|
| Id | uniqueidentifier | PK | 主键 |
| Name | nvarchar(100) | NOT NULL | 数据源名称 |
| Description | nvarchar(500) | NULL | 数据源描述 |
| SourceType | nvarchar(10) | NOT NULL | 源类型（TXT、XLSX、CSV） |
| FilePath | nvarchar(500) | NOT NULL | 文件路径 |
| ImportedAt | datetime2 | NOT NULL | 导入时间 |
| ImportedBy | uniqueidentifier | NOT NULL | 导入人ID |
| TotalRows | int | NOT NULL | 总行数 |
| IsProcessed | bit | NOT NULL | 是否已处理 |
| ProcessingError | nvarchar(1000) | NULL | 处理错误信息 |
| CreatedAt | datetime2 | NOT NULL | 创建时间 |
| ModifiedAt | datetime2 | NULL | 修改时间 |
| CreatedBy | nvarchar(50) | NULL | 创建人 |
| ModifiedBy | nvarchar(50) | NULL | 修改人 |

**索引**：
- PK_DataSources (Id)
- IX_DataSources_Name (Name)
- IX_DataSources_ImportedAt (ImportedAt)
- IX_DataSources_ImportedBy (ImportedBy)

#### DataCategories（数据类别表）

| 列名 | 数据类型 | 约束 | 描述 |
|------|----------|------|------|
| Id | uniqueidentifier | PK | 主键 |
| Name | nvarchar(100) | NOT NULL, UQ | 类别名称，唯一 |
| Description | nvarchar(500) | NULL | 类别描述 |
| Unit | nvarchar(50) | NULL | 单位 |
| IsActive | bit | NOT NULL | 是否激活 |
| CreatedAt | datetime2 | NOT NULL | 创建时间 |
| ModifiedAt | datetime2 | NULL | 修改时间 |
| CreatedBy | nvarchar(50) | NULL | 创建人 |
| ModifiedBy | nvarchar(50) | NULL | 修改人 |

**索引**：
- PK_DataCategories (Id)
- UQ_DataCategories_Name (Name)

#### DataPoints（数据点表）

| 列名 | 数据类型 | 约束 | 描述 |
|------|----------|------|------|
| Id | uniqueidentifier | PK | 主键 |
| DataSourceId | uniqueidentifier | NOT NULL, FK | 数据源ID，外键 |
| DataCategoryId | uniqueidentifier | NOT NULL, FK | 数据类别ID，外键 |
| Value | float | NOT NULL | 数值 |
| Timestamp | datetime2 | NOT NULL | 时间戳 |
| Label | nvarchar(100) | NULL | 标签 |
| IsAbnormal | bit | NOT NULL | 是否异常 |
| AbnormalReason | nvarchar(200) | NULL | 异常原因 |
| CreatedAt | datetime2 | NOT NULL | 创建时间 |
| ModifiedAt | datetime2 | NULL | 修改时间 |
| CreatedBy | nvarchar(50) | NULL | 创建人 |
| ModifiedBy | nvarchar(50) | NULL | 修改人 |

**索引**：
- PK_DataPoints (Id)
- IX_DataPoints_DataSourceId (DataSourceId)
- IX_DataPoints_DataCategoryId (DataCategoryId)
- IX_DataPoints_Timestamp (Timestamp)
- IX_DataPoints_IsAbnormal (IsAbnormal)

**外键**：
- FK_DataPoints_DataSources (DataSourceId) REFERENCES DataSources(Id)
- FK_DataPoints_DataCategories (DataCategoryId) REFERENCES DataCategories(Id)

#### Thresholds（阈值表）

| 列名 | 数据类型 | 约束 | 描述 |
|------|----------|------|------|
| Id | uniqueidentifier | PK | 主键 |
| DataCategoryId | uniqueidentifier | NOT NULL, FK | 数据类别ID，外键 |
| Name | nvarchar(100) | NOT NULL | 阈值名称 |
| Description | nvarchar(500) | NULL | 阈值描述 |
| MinValue | float | NOT NULL | 最小值 |
| MaxValue | float | NOT NULL | 最大值 |
| Color | nvarchar(20) | NOT NULL | 颜色代码 |
| IsActive | bit | NOT NULL | 是否激活 |
| CreatedAt | datetime2 | NOT NULL | 创建时间 |
| ModifiedAt | datetime2 | NULL | 修改时间 |
| CreatedBy | nvarchar(50) | NULL | 创建人 |
| ModifiedBy | nvarchar(50) | NULL | 修改人 |

**索引**：
- PK_Thresholds (Id)
- IX_Thresholds_DataCategoryId (DataCategoryId)
- IX_Thresholds_IsActive (IsActive)

**外键**：
- FK_Thresholds_DataCategories (DataCategoryId) REFERENCES DataCategories(Id)

### 2.3 可视化领域表

#### Charts（图表表）

| 列名 | 数据类型 | 约束 | 描述 |
|------|----------|------|------|
| Id | uniqueidentifier | PK | 主键 |
| Title | nvarchar(100) | NOT NULL | 图表标题 |
| Description | nvarchar(500) | NULL | 图表描述 |
| ChartType | nvarchar(20) | NOT NULL | 图表类型 |
| CreatedBy | uniqueidentifier | NOT NULL | 创建人ID |
| IsPublic | bit | NOT NULL | 是否公开 |
| ConfigJson | nvarchar(max) | NULL | 配置JSON |
| CreatedAt | datetime2 | NOT NULL | 创建时间 |
| ModifiedAt | datetime2 | NULL | 修改时间 |
| CreatedBy | nvarchar(50) | NULL | 创建人 |
| ModifiedBy | nvarchar(50) | NULL | 修改人 |

**索引**：
- PK_Charts (Id)
- IX_Charts_CreatedBy (CreatedBy)
- IX_Charts_ChartType (ChartType)
- IX_Charts_IsPublic (IsPublic)

#### Dashboards（仪表盘表）

| 列名 | 数据类型 | 约束 | 描述 |
|------|----------|------|------|
| Id | uniqueidentifier | PK | 主键 |
| Title | nvarchar(100) | NOT NULL | 仪表盘标题 |
| Description | nvarchar(500) | NULL | 仪表盘描述 |
| CreatedBy | uniqueidentifier | NOT NULL | 创建人ID |
| IsPublic | bit | NOT NULL | 是否公开 |
| LayoutJson | nvarchar(max) | NULL | 布局JSON |
| CreatedAt | datetime2 | NOT NULL | 创建时间 |
| ModifiedAt | datetime2 | NULL | 修改时间 |
| CreatedBy | nvarchar(50) | NULL | 创建人 |
| ModifiedBy | nvarchar(50) | NULL | 修改人 |

**索引**：
- PK_Dashboards (Id)
- IX_Dashboards_CreatedBy (CreatedBy)
- IX_Dashboards_IsPublic (IsPublic)

#### ChartDataCategories（图表-数据类别关联表）

| 列名 | 数据类型 | 约束 | 描述 |
|------|----------|------|------|
| Id | uniqueidentifier | PK | 主键 |
| ChartId | uniqueidentifier | NOT NULL, FK | 图表ID，外键 |
| DataCategoryId | uniqueidentifier | NOT NULL, FK | 数据类别ID，外键 |
| CreatedAt | datetime2 | NOT NULL | 创建时间 |
| ModifiedAt | datetime2 | NULL | 修改时间 |
| CreatedBy | nvarchar(50) | NULL | 创建人 |
| ModifiedBy | nvarchar(50) | NULL | 修改人 |

**索引**：
- PK_ChartDataCategories (Id)
- UQ_ChartDataCategories_ChartId_DataCategoryId (ChartId, DataCategoryId)
- IX_ChartDataCategories_ChartId (ChartId)
- IX_ChartDataCategories_DataCategoryId (DataCategoryId)

**外键**：
- FK_ChartDataCategories_Charts (ChartId) REFERENCES Charts(Id)
- FK_ChartDataCategories_DataCategories (DataCategoryId) REFERENCES DataCategories(Id)

#### DashboardCharts（仪表盘-图表关联表）

| 列名 | 数据类型 | 约束 | 描述 |
|------|----------|------|------|
| Id | uniqueidentifier | PK | 主键 |
| DashboardId | uniqueidentifier | NOT NULL, FK | 仪表盘ID，外键 |
| ChartId | uniqueidentifier | NOT NULL, FK | 图表ID，外键 |
| PositionX | int | NOT NULL | X位置 |
| PositionY | int | NOT NULL | Y位置 |
| Width | int | NOT NULL | 宽度 |
| Height | int | NOT NULL | 高度 |
| CreatedAt | datetime2 | NOT NULL | 创建时间 |
| ModifiedAt | datetime2 | NULL | 修改时间 |
| CreatedBy | nvarchar(50) | NULL | 创建人 |
| ModifiedBy | nvarchar(50) | NULL | 修改人 |

**索引**：
- PK_DashboardCharts (Id)
- UQ_DashboardCharts_DashboardId_ChartId (DashboardId, ChartId)
- IX_DashboardCharts_DashboardId (DashboardId)
- IX_DashboardCharts_ChartId (ChartId)

**外键**：
- FK_DashboardCharts_Dashboards (DashboardId) REFERENCES Dashboards(Id)
- FK_DashboardCharts_Charts (ChartId) REFERENCES Charts(Id)

## 3. 数据库关系模型

```mermaid
erDiagram
    Users ||--o{ UserRoles : has
    Roles ||--o{ UserRoles : has
    Roles ||--o{ RolePermissions : has
    Permissions ||--o{ RolePermissions : has
    
    DataSources ||--o{ DataPoints : contains
    DataCategories ||--o{ DataPoints : categorizes
    DataCategories ||--o{ Thresholds : has
    
    Charts ||--o{ ChartDataCategories : uses
    DataCategories ||--o{ ChartDataCategories : usedBy
    Charts ||--o{ DashboardCharts : displayedIn
    Dashboards ||--o{ DashboardCharts : displays
    
    Users {
        uniqueidentifier Id PK
        string Username
        string Email
        string PasswordHash
        string FullName
        boolean IsActive
        datetime LastLoginTime
        datetime CreatedAt
        datetime ModifiedAt
        string CreatedBy
        string ModifiedBy
    }
    
    Roles {
        uniqueidentifier Id PK
        string Name
        string Description
        datetime CreatedAt
        datetime ModifiedAt
        string CreatedBy
        string ModifiedBy
    }
    
    Permissions {
        uniqueidentifier Id PK
        string Name
        string Description
        string Code
        datetime CreatedAt
        datetime ModifiedAt
        string CreatedBy
        string ModifiedBy
    }
    
    UserRoles {
        uniqueidentifier Id PK
        uniqueidentifier UserId FK
        uniqueidentifier RoleId FK
        datetime CreatedAt
        datetime ModifiedAt
        string CreatedBy
        string ModifiedBy
    }
    
    RolePermissions {
        uniqueidentifier Id PK
        uniqueidentifier RoleId FK
        uniqueidentifier PermissionId FK
        datetime CreatedAt
        datetime ModifiedAt
        string CreatedBy
        string ModifiedBy
    }
    
    DataSources {
        uniqueidentifier Id PK
        string Name
        string Description
        string SourceType
        string FilePath
        datetime ImportedAt
        uniqueidentifier ImportedBy
        int TotalRows
        boolean IsProcessed
        string ProcessingError
        datetime CreatedAt
        datetime ModifiedAt
        string CreatedBy
        string ModifiedBy
    }
    
    DataCategories {
        uniqueidentifier Id PK
        string Name
        string Description
        string Unit
        boolean IsActive
        datetime CreatedAt
        datetime ModifiedAt
        string CreatedBy
        string ModifiedBy
    }
    
    DataPoints {
        uniqueidentifier Id PK
        uniqueidentifier DataSourceId FK
        uniqueidentifier DataCategoryId FK
        float Value
        datetime Timestamp
        string Label
        boolean IsAbnormal
        string AbnormalReason
        datetime CreatedAt
        datetime ModifiedAt
        string CreatedBy
        string ModifiedBy
    }
    
    Thresholds {
        uniqueidentifier Id PK
        uniqueidentifier DataCategoryId FK
        string Name
        string Description
        float MinValue
        float MaxValue
        string Color
        boolean IsActive
        datetime CreatedAt
        datetime ModifiedAt
        string CreatedBy
        string ModifiedBy
    }
    
    Charts {
        uniqueidentifier Id PK
        string Title
        string Description
        string ChartType
        uniqueidentifier CreatedBy
        boolean IsPublic
        string ConfigJson
        datetime CreatedAt
        datetime ModifiedAt
        string CreatedBy
        string ModifiedBy
    }
    
    Dashboards {
        uniqueidentifier Id PK
        string Title
        string Description
        uniqueidentifier CreatedBy
        boolean IsPublic
        string LayoutJson
        datetime CreatedAt
        datetime ModifiedAt
        string CreatedBy
        string ModifiedBy
    }
    
    ChartDataCategories {
        uniqueidentifier Id PK
        uniqueidentifier ChartId FK
        uniqueidentifier DataCategoryId FK
        datetime CreatedAt
        datetime ModifiedAt
        string CreatedBy
        string ModifiedBy
    }
    
    DashboardCharts {
        uniqueidentifier Id PK
        uniqueidentifier DashboardId FK
        uniqueidentifier ChartId FK
        int PositionX
        int PositionY
        int Width
        int Height
        datetime CreatedAt
        datetime ModifiedAt
        string CreatedBy
        string ModifiedBy
    }
```

## 4. 数据库种子数据

### 4.1 角色和权限种子数据

#### 角色

| Id | Name | Description |
|----|------|-------------|
| 8D04DCE2-969A-435D-BBA4-DF3F325983DC | 管理员 | 系统管理员，拥有所有权限 |
| 8D04DCE2-969A-435D-BBA4-DF3F325983DD | 数据导入员 | 负责导入数据源文件 |
| 8D04DCE2-969A-435D-BBA4-DF3F325983DE | 观看者 | 只能查看数据和图表 |

#### 权限

| Id | Name | Description | Code |
|----|------|-------------|------|
| 8D04DCE2-969A-435D-BBA4-DF3F325983E0 | 管理用户 | 管理系统用户 | MANAGE_USERS |
| 8D04DCE2-969A-435D-BBA4-DF3F325983E1 | 导入数据 | 导入数据源文件 | IMPORT_DATA |
| 8D04DCE2-969A-435D-BBA4-DF3F325983E2 | 查看仪表盘 | 查看数据仪表盘 | VIEW_DASHBOARDS |
| 8D04DCE2-969A-435D-BBA4-DF3F325983E3 | 管理图表 | 创建和管理图表 | MANAGE_CHARTS |
| 8D04DCE2-969A-435D-BBA4-DF3F325983E4 | 管理系统 | 管理系统设置 | MANAGE_SYSTEM |

#### 角色-权限关联

| RoleId | PermissionId |
|--------|-------------|
| 8D04DCE2-969A-435D-BBA4-DF3F325983DC | 8D04DCE2-969A-435D-BBA4-DF3F325983E0 |
| 8D04DCE2-969A-435D-BBA4-DF3F325983DC | 8D04DCE2-969A-435D-BBA4-DF3F325983E1 |
| 8D04DCE2-969A-435D-BBA4-DF3F325983DC | 8D04DCE2-969A-435D-BBA4-DF3F325983E2 |
| 8D04DCE2-969A-435D-BBA4-DF3F325983DC | 8D04DCE2-969A-435D-BBA4-DF3F325983E3 |
| 8D04DCE2-969A-435D-BBA4-DF3F325983DC | 8D04DCE2-969A-435D-BBA4-DF3F325983E4 |
| 8D04DCE2-969A-435D-BBA4-DF3F325983DD | 8D04DCE2-969A-435D-BBA4-DF3F325983E1 |
| 8D04DCE2-969A-435D-BBA4-DF3F325983DD | 8D04DCE2-969A-435D-BBA4-DF3F325983E2 |
| 8D04DCE2-969A-435D-BBA4-DF3F325983DE | 8D04DCE2-969A-435D-BBA4-DF3F325983E2 |

## 5. 数据库性能优化策略

### 5.1 索引优化

- 为所有外键列创建索引
- 为常用查询条件创建索引（如IsActive、ImportedAt等）
- 为唯一约束创建唯一索引

### 5.2 查询优化

- 使用分页查询大数据集
- 使用索引列作为查询条件
- 避免使用SELECT *，只选择需要的列

### 5.3 数据分区

- 对DataPoints表按时间范围进行分区，提高大数据量下的查询性能
- 考虑使用表分区或分区视图

### 5.4 数据库维护

- 定期更新统计信息
- 定期重建索引
- 监控数据库性能

## 6. 数据库安全策略

### 6.1 访问控制

- 使用最小权限原则
- 使用数据库角色控制访问权限
- 避免使用sa账户

### 6.2 数据加密

- 敏感数据（如密码）使用哈希存储
- 考虑使用列级加密保护敏感数据

### 6.3 审计跟踪

- 使用CreatedAt、ModifiedAt、CreatedBy、ModifiedBy列记录审计信息
- 考虑使用数据库触发器记录数据变更

## 7. 总结

本数据库设计遵循了关系数据库设计的最佳实践，包括规范化、索引优化和安全策略。该设计支持系统的核心功能，包括用户管理、数据导入和可视化展示。通过合理的表结构和索引设计，系统可以高效地处理和查询大量数据，为生产数据可视化提供可靠的数据存储基础。 