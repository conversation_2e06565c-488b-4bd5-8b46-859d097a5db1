<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>测试页面</title>
  <style>
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
      margin: 0;
      padding: 20px;
      background-color: #f0f2f5;
    }
    .container {
      max-width: 800px;
      margin: 0 auto;
      background-color: white;
      padding: 20px;
      border-radius: 8px;
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    }
    h1 {
      color: #1890ff;
    }
    .button {
      background-color: #1890ff;
      color: white;
      border: none;
      padding: 10px 20px;
      border-radius: 4px;
      cursor: pointer;
      font-size: 16px;
      margin-top: 20px;
    }
    .button:hover {
      background-color: #40a9ff;
    }
  </style>
</head>
<body>
  <div class="container">
    <h1>生产数据可视化系统 - 测试页面</h1>
    <p>如果您能看到此页面，说明静态文件服务正常工作。</p>
    <p>这是一个简单的HTML测试页面，用于验证服务器是否正确提供静态文件。</p>
    <p>当前时间: <span id="current-time"></span></p>
    <button class="button" onclick="testFunction()">测试按钮</button>
    <div id="test-result" style="margin-top: 20px;"></div>
  </div>

  <script>
    // 显示当前时间
    function updateTime() {
      document.getElementById('current-time').textContent = new Date().toLocaleString();
    }
    updateTime();
    setInterval(updateTime, 1000);

    // 测试按钮点击事件
    function testFunction() {
      document.getElementById('test-result').innerHTML = 
        '<p style="color: green;">测试成功！JavaScript正常工作。</p>';
    }
  </script>
</body>
</html> 