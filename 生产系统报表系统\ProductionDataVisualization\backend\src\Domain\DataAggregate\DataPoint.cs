using System;
using Domain.Common;

namespace Domain.DataAggregate
{
    /// <summary>
    /// 数据点实体
    /// </summary>
    public class DataPoint : Entity
    {
        public Guid DataSourceId { get; private set; }
        public Guid DataCategoryId { get; private set; }
        public double Value { get; private set; }
        public DateTime Timestamp { get; private set; }
        public string? Label { get; private set; }
        public bool IsAbnormal { get; private set; }
        public string? AbnormalReason { get; private set; }

        public DataSource DataSource { get; private set; }
        public DataCategory DataCategory { get; private set; }

        // 防止无参构造函数被外部调用
        private DataPoint() { }

        public DataPoint(
            DataSource dataSource,
            DataCategory dataCategory,
            double value,
            DateTime timestamp,
            string? label = null)
        {
            if (dataSource == null)
                throw new ArgumentNullException(nameof(dataSource));

            if (dataCategory == null)
                throw new ArgumentNullException(nameof(dataCategory));

            DataSourceId = dataSource.Id;
            DataCategoryId = dataCategory.Id;
            DataSource = dataSource;
            DataCategory = dataCategory;
            Value = value;
            Timestamp = timestamp;
            Label = label;
            IsAbnormal = false;
        }

        public void MarkAsAbnormal(string reason)
        {
            IsAbnormal = true;
            AbnormalReason = reason;
            ModifiedAt = DateTime.UtcNow;
        }

        public void MarkAsNormal()
        {
            IsAbnormal = false;
            AbnormalReason = null;
            ModifiedAt = DateTime.UtcNow;
        }

        public void UpdateValue(double value)
        {
            Value = value;
            ModifiedAt = DateTime.UtcNow;
        }
    }
} 