using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Domain.Common;

namespace Domain.VisualizationAggregate
{
    /// <summary>
    /// 图表仓储接口
    /// </summary>
    public interface IChartRepository : IRepository<Chart>
    {
        Task<IEnumerable<Chart>> GetByCreatedByAsync(Guid userId);
        Task<IEnumerable<Chart>> GetPublicAsync();
        Task<IEnumerable<Chart>> GetByDataCategoryIdAsync(Guid dataCategoryId);
        Task<IEnumerable<Chart>> GetByChartTypeAsync(string chartType);
    }
} 