using Microsoft.EntityFrameworkCore;

namespace Infrastructure.Persistence.Extensions
{
    /// <summary>
    /// ModelBuilder扩展方法
    /// </summary>
    public static class ModelBuilderExtensions
    {
        /// <summary>
        /// 配置性能优化索引
        /// </summary>
        /// <param name="modelBuilder">模型构建器</param>
        public static void ConfigurePerformanceIndexes(this ModelBuilder modelBuilder)
        {
            // 用户表索引
            modelBuilder.Entity<Domain.UserAggregate.User>(entity =>
            {
                // 用户名唯一索引
                entity.HasIndex(u => u.Username)
                    .IsUnique()
                    .HasDatabaseName("IX_Users_Username");

                // 邮箱唯一索引
                entity.HasIndex(u => u.Email)
                    .IsUnique()
                    .HasDatabaseName("IX_Users_Email");

                // 活跃用户索引
                entity.HasIndex(u => u.IsActive)
                    .HasDatabaseName("IX_Users_IsActive");

                // 最后登录时间索引
                entity.HasIndex(u => u.LastLoginTime)
                    .HasDatabaseName("IX_Users_LastLoginTime");

                // 复合索引：活跃状态 + 创建时间
                entity.HasIndex(u => new { u.IsActive, u.CreatedAt })
                    .HasDatabaseName("IX_Users_IsActive_CreatedAt");
            });

            // 角色表索引
            modelBuilder.Entity<Domain.UserAggregate.Role>(entity =>
            {
                // 角色名唯一索引
                entity.HasIndex(r => r.Name)
                    .IsUnique()
                    .HasDatabaseName("IX_Roles_Name");
            });

            // 权限表索引
            modelBuilder.Entity<Domain.UserAggregate.Permission>(entity =>
            {
                // 权限代码唯一索引
                entity.HasIndex(p => p.Code)
                    .IsUnique()
                    .HasDatabaseName("IX_Permissions_Code");

                // 权限名索引
                entity.HasIndex(p => p.Name)
                    .HasDatabaseName("IX_Permissions_Name");
            });

            // 用户角色关联表索引
            modelBuilder.Entity<Domain.UserAggregate.UserRole>(entity =>
            {
                // 用户ID + 角色ID 唯一复合索引
                entity.HasIndex(ur => new { ur.UserId, ur.RoleId })
                    .IsUnique()
                    .HasDatabaseName("IX_UserRoles_UserId_RoleId");

                // 角色ID索引（用于反向查询）
                entity.HasIndex(ur => ur.RoleId)
                    .HasDatabaseName("IX_UserRoles_RoleId");
            });

            // 角色权限关联表索引
            modelBuilder.Entity<Domain.UserAggregate.RolePermission>(entity =>
            {
                // 角色ID + 权限ID 唯一复合索引
                entity.HasIndex(rp => new { rp.RoleId, rp.PermissionId })
                    .IsUnique()
                    .HasDatabaseName("IX_RolePermissions_RoleId_PermissionId");

                // 权限ID索引（用于反向查询）
                entity.HasIndex(rp => rp.PermissionId)
                    .HasDatabaseName("IX_RolePermissions_PermissionId");
            });

            // 数据源表索引
            modelBuilder.Entity<Domain.DataAggregate.DataSource>(entity =>
            {
                // 数据源名索引
                entity.HasIndex(ds => ds.Name)
                    .HasDatabaseName("IX_DataSources_Name");

                // 导入时间索引
                entity.HasIndex(ds => ds.ImportedAt)
                    .HasDatabaseName("IX_DataSources_ImportedAt");

                // 导入人索引
                entity.HasIndex(ds => ds.ImportedBy)
                    .HasDatabaseName("IX_DataSources_ImportedBy");

                // 处理状态索引
                entity.HasIndex(ds => ds.IsProcessed)
                    .HasDatabaseName("IX_DataSources_IsProcessed");

                // 复合索引：处理状态 + 导入时间
                entity.HasIndex(ds => new { ds.IsProcessed, ds.ImportedAt })
                    .HasDatabaseName("IX_DataSources_IsProcessed_ImportedAt");

                // 源类型索引
                entity.HasIndex(ds => ds.SourceType)
                    .HasDatabaseName("IX_DataSources_SourceType");
            });

            // 数据类别表索引
            modelBuilder.Entity<Domain.DataAggregate.DataCategory>(entity =>
            {
                // 类别名唯一索引
                entity.HasIndex(dc => dc.Name)
                    .IsUnique()
                    .HasDatabaseName("IX_DataCategories_Name");

                // 活跃状态索引
                entity.HasIndex(dc => dc.IsActive)
                    .HasDatabaseName("IX_DataCategories_IsActive");
            });

            // 数据点表索引
            modelBuilder.Entity<Domain.DataAggregate.DataPoint>(entity =>
            {
                // 数据源ID索引
                entity.HasIndex(dp => dp.DataSourceId)
                    .HasDatabaseName("IX_DataPoints_DataSourceId");

                // 数据类别ID索引
                entity.HasIndex(dp => dp.DataCategoryId)
                    .HasDatabaseName("IX_DataPoints_DataCategoryId");

                // 时间戳索引
                entity.HasIndex(dp => dp.Timestamp)
                    .HasDatabaseName("IX_DataPoints_Timestamp");

                // 异常状态索引
                entity.HasIndex(dp => dp.IsAbnormal)
                    .HasDatabaseName("IX_DataPoints_IsAbnormal");

                // 复合索引：数据类别 + 时间戳
                entity.HasIndex(dp => new { dp.DataCategoryId, dp.Timestamp })
                    .HasDatabaseName("IX_DataPoints_DataCategoryId_Timestamp");

                // 复合索引：数据源 + 时间戳
                entity.HasIndex(dp => new { dp.DataSourceId, dp.Timestamp })
                    .HasDatabaseName("IX_DataPoints_DataSourceId_Timestamp");

                // 复合索引：异常状态 + 时间戳
                entity.HasIndex(dp => new { dp.IsAbnormal, dp.Timestamp })
                    .HasDatabaseName("IX_DataPoints_IsAbnormal_Timestamp");

                // 复合索引：数据类别 + 异常状态 + 时间戳
                entity.HasIndex(dp => new { dp.DataCategoryId, dp.IsAbnormal, dp.Timestamp })
                    .HasDatabaseName("IX_DataPoints_DataCategoryId_IsAbnormal_Timestamp");
            });

            // 阈值表索引
            modelBuilder.Entity<Domain.DataAggregate.Threshold>(entity =>
            {
                // 数据类别ID索引
                entity.HasIndex(t => t.DataCategoryId)
                    .HasDatabaseName("IX_Thresholds_DataCategoryId");

                // 活跃状态索引
                entity.HasIndex(t => t.IsActive)
                    .HasDatabaseName("IX_Thresholds_IsActive");

                // 复合索引：数据类别 + 活跃状态
                entity.HasIndex(t => new { t.DataCategoryId, t.IsActive })
                    .HasDatabaseName("IX_Thresholds_DataCategoryId_IsActive");
            });

            // 图表表索引
            modelBuilder.Entity<Domain.VisualizationAggregate.Chart>(entity =>
            {
                // 创建人索引
                entity.HasIndex(c => c.CreatedBy)
                    .HasDatabaseName("IX_Charts_CreatedBy");

                // 图表类型索引
                entity.HasIndex(c => c.ChartType)
                    .HasDatabaseName("IX_Charts_ChartType");

                // 公开状态索引
                entity.HasIndex(c => c.IsPublic)
                    .HasDatabaseName("IX_Charts_IsPublic");

                // 复合索引：公开状态 + 创建时间
                entity.HasIndex(c => new { c.IsPublic, c.CreatedAt })
                    .HasDatabaseName("IX_Charts_IsPublic_CreatedAt");
            });

            // 仪表盘表索引
            modelBuilder.Entity<Domain.VisualizationAggregate.Dashboard>(entity =>
            {
                // 创建人索引
                entity.HasIndex(d => d.CreatedBy)
                    .HasDatabaseName("IX_Dashboards_CreatedBy");

                // 公开状态索引
                entity.HasIndex(d => d.IsPublic)
                    .HasDatabaseName("IX_Dashboards_IsPublic");

                // 复合索引：公开状态 + 创建时间
                entity.HasIndex(d => new { d.IsPublic, d.CreatedAt })
                    .HasDatabaseName("IX_Dashboards_IsPublic_CreatedAt");
            });

            // 图表数据类别关联表索引
            modelBuilder.Entity<Domain.VisualizationAggregate.ChartDataCategory>(entity =>
            {
                // 图表ID + 数据类别ID 唯一复合索引
                entity.HasIndex(cdc => new { cdc.ChartId, cdc.DataCategoryId })
                    .IsUnique()
                    .HasDatabaseName("IX_ChartDataCategories_ChartId_DataCategoryId");

                // 数据类别ID索引（用于反向查询）
                entity.HasIndex(cdc => cdc.DataCategoryId)
                    .HasDatabaseName("IX_ChartDataCategories_DataCategoryId");
            });

            // 仪表盘图表关联表索引
            modelBuilder.Entity<Domain.VisualizationAggregate.DashboardChart>(entity =>
            {
                // 仪表盘ID + 图表ID 唯一复合索引
                entity.HasIndex(dc => new { dc.DashboardId, dc.ChartId })
                    .IsUnique()
                    .HasDatabaseName("IX_DashboardCharts_DashboardId_ChartId");

                // 图表ID索引（用于反向查询）
                entity.HasIndex(dc => dc.ChartId)
                    .HasDatabaseName("IX_DashboardCharts_ChartId");
            });
        }

        /// <summary>
        /// 配置审计字段索引
        /// </summary>
        /// <param name="modelBuilder">模型构建器</param>
        public static void ConfigureAuditIndexes(this ModelBuilder modelBuilder)
        {
            // 为所有实体添加创建时间和修改时间索引
            var entityTypes = modelBuilder.Model.GetEntityTypes()
                .Where(t => t.ClrType.IsSubclassOf(typeof(Domain.Common.Entity)));

            foreach (var entityType in entityTypes)
            {
                var tableName = entityType.GetTableName();
                
                // 创建时间索引
                modelBuilder.Entity(entityType.ClrType)
                    .HasIndex("CreatedAt")
                    .HasDatabaseName($"IX_{tableName}_CreatedAt");

                // 修改时间索引
                modelBuilder.Entity(entityType.ClrType)
                    .HasIndex("ModifiedAt")
                    .HasDatabaseName($"IX_{tableName}_ModifiedAt");

                // 创建人索引
                modelBuilder.Entity(entityType.ClrType)
                    .HasIndex("CreatedBy")
                    .HasDatabaseName($"IX_{tableName}_CreatedBy");
            }
        }
    }
}
