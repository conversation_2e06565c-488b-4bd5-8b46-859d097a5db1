using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Domain.Common;
using Domain.VisualizationAggregate;
using Microsoft.EntityFrameworkCore;

namespace Infrastructure.Persistence.Repositories
{
    /// <summary>
    /// 图表仓储实现
    /// </summary>
    public class ChartRepository : BaseRepository<Chart>, IChartRepository
    {
        public ChartRepository(ApplicationDbContext dbContext) : base(dbContext)
        {
        }

        public override async Task<Chart> GetByIdAsync(Guid id)
        {
            return await DbContext.Charts
                .Include(c => c.ChartDataCategories)
                .FirstOrDefaultAsync(c => c.Id == id);
        }

        public override async Task<IEnumerable<Chart>> GetAllAsync()
        {
            return await DbContext.Charts
                .Include(c => c.ChartDataCategories)
                .ToListAsync();
        }

        public async Task<IEnumerable<Chart>> GetByCreatedByAsync(Guid userId)
        {
            return await DbContext.Charts
                .Where(c => c.CreatedBy == userId)
                .Include(c => c.ChartDataCategories)
                .OrderByDescending(c => c.CreatedAt)
                .ToListAsync();
        }

        public async Task<IEnumerable<Chart>> GetByDataCategoryIdAsync(Guid dataCategoryId)
        {
            return await DbContext.Charts
                .Include(c => c.ChartDataCategories)
                .Where(c => c.ChartDataCategories.Any(cdc => cdc.DataCategoryId == dataCategoryId))
                .ToListAsync();
        }

        public async Task<IEnumerable<Chart>> GetPublicAsync()
        {
            return await DbContext.Charts
                .Include(c => c.ChartDataCategories)
                .Where(c => c.IsPublic)
                .ToListAsync();
        }

        public async Task<IEnumerable<Chart>> GetByChartTypeAsync(string chartType)
        {
            return await DbContext.Charts
                .Include(c => c.ChartDataCategories)
                .Where(c => c.ChartType == chartType)
                .ToListAsync();
        }
    }
} 