using System;
using System.Collections.Generic;
using Domain.Common;

namespace Domain.UserAggregate
{
    /// <summary>
    /// 权限实体
    /// </summary>
    public class Permission : Entity, IAggregateRoot
    {
        public string Name { get; private set; }
        public string Description { get; private set; }
        public string Code { get; private set; }

        private readonly List<RolePermission> _rolePermissions = new();
        public IReadOnlyCollection<RolePermission> RolePermissions => _rolePermissions.AsReadOnly();

        // 预定义权限
        public static readonly Guid ManageUsersPermissionId = new Guid("8D04DCE2-969A-435D-BBA4-DF3F325983E0");
        public static readonly Guid ImportDataPermissionId = new Guid("8D04DCE2-969A-435D-BBA4-DF3F325983E1");
        public static readonly Guid ViewDashboardsPermissionId = new Guid("8D04DCE2-969A-435D-BBA4-DF3F325983E2");
        public static readonly Guid ManageChartsPermissionId = new Guid("8D04DCE2-969A-435D-BBA4-DF3F325983E3");
        public static readonly Guid ManageSystemPermissionId = new Guid("8D04DCE2-969A-435D-BBA4-DF3F325983E4");

        // 防止无参构造函数被外部调用
        private Permission() { }

        public Permission(string name, string description, string code)
        {
            if (string.IsNullOrWhiteSpace(name))
                throw new ArgumentException("权限名称不能为空", nameof(name));

            if (string.IsNullOrWhiteSpace(code))
                throw new ArgumentException("权限代码不能为空", nameof(code));

            Name = name;
            Description = description;
            Code = code;
        }

        public Permission(Guid id, string name, string description, string code) : base(id)
        {
            if (string.IsNullOrWhiteSpace(name))
                throw new ArgumentException("权限名称不能为空", nameof(name));

            if (string.IsNullOrWhiteSpace(code))
                throw new ArgumentException("权限代码不能为空", nameof(code));

            Name = name;
            Description = description;
            Code = code;
        }

        public void Update(string name, string description)
        {
            if (!string.IsNullOrWhiteSpace(name))
                Name = name;

            if (description != null)
                Description = description;

            ModifiedAt = DateTime.UtcNow;
        }
    }
} 