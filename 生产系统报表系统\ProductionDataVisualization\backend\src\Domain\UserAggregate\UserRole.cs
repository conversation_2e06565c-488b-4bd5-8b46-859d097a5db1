using System;
using Domain.Common;

namespace Domain.UserAggregate
{
    /// <summary>
    /// 用户-角色关联实体
    /// </summary>
    public class UserRole : Entity
    {
        public Guid UserId { get; private set; }
        public Guid RoleId { get; private set; }

        public User User { get; private set; }
        public Role Role { get; private set; }

        // 防止无参构造函数被外部调用
        private UserRole() { }

        public UserRole(User user, Role role)
        {
            if (user == null)
                throw new ArgumentNullException(nameof(user));
            
            if (role == null)
                throw new ArgumentNullException(nameof(role));

            UserId = user.Id;
            RoleId = role.Id;
            User = user;
            Role = role;
        }
    }
} 