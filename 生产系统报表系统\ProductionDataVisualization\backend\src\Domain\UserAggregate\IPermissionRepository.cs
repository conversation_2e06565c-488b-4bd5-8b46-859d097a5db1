using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Domain.Common;

namespace Domain.UserAggregate
{
    /// <summary>
    /// 权限仓储接口
    /// </summary>
    public interface IPermissionRepository : IRepository<Permission>
    {
        Task<Permission> GetByNameAsync(string name);
        Task<IEnumerable<Permission>> GetByRoleIdAsync(Guid roleId);
        Task<IEnumerable<Permission>> GetRolePermissionsAsync(Guid roleId);
        Task<IEnumerable<Permission>> GetByUserIdAsync(Guid userId);
    }
} 