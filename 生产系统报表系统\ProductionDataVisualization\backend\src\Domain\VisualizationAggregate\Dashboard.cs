using System;
using System.Collections.Generic;
using Domain.Common;

namespace Domain.VisualizationAggregate
{
    /// <summary>
    /// 仪表盘实体，作为聚合根
    /// </summary>
    public class Dashboard : Entity, IAggregateRoot
    {
        public string Title { get; private set; }
        public string Description { get; private set; }
        public Guid CreatedBy { get; private set; }
        public bool IsPublic { get; private set; }
        public string? LayoutJson { get; private set; } // 仪表盘布局的JSON字符串

        private readonly List<DashboardChart> _dashboardCharts = new();
        public IReadOnlyCollection<DashboardChart> DashboardCharts => _dashboardCharts.AsReadOnly();

        // 防止无参构造函数被外部调用
        private Dashboard() { }

        public Dashboard(
            string title,
            string description,
            Guid createdBy,
            bool isPublic = false,
            string? layoutJson = null)
        {
            if (string.IsNullOrWhiteSpace(title))
                throw new ArgumentException("仪表盘标题不能为空", nameof(title));

            Title = title;
            Description = description ?? string.Empty;
            CreatedBy = createdBy;
            IsPublic = isPublic;
            LayoutJson = layoutJson;
        }

        public void Update(string title, string description, bool isPublic, string? layoutJson)
        {
            if (!string.IsNullOrWhiteSpace(title))
                Title = title;

            if (description != null)
                Description = description;

            IsPublic = isPublic;
            
            if (layoutJson != null)
                LayoutJson = layoutJson;

            ModifiedAt = DateTime.UtcNow;
        }

        public void AddChart(Chart chart, int positionX, int positionY, int width, int height)
        {
            if (chart == null)
                throw new ArgumentNullException(nameof(chart));

            if (!_dashboardCharts.Exists(dc => dc.ChartId == chart.Id))
            {
                _dashboardCharts.Add(new DashboardChart(this, chart, positionX, positionY, width, height));
                ModifiedAt = DateTime.UtcNow;
            }
        }

        public void RemoveChart(Chart chart)
        {
            if (chart == null)
                throw new ArgumentNullException(nameof(chart));

            var dashboardChart = _dashboardCharts.Find(dc => dc.ChartId == chart.Id);
            if (dashboardChart != null)
            {
                _dashboardCharts.Remove(dashboardChart);
                ModifiedAt = DateTime.UtcNow;
            }
        }

        public void UpdateChartPosition(Chart chart, int positionX, int positionY, int width, int height)
        {
            if (chart == null)
                throw new ArgumentNullException(nameof(chart));

            var dashboardChart = _dashboardCharts.Find(dc => dc.ChartId == chart.Id);
            if (dashboardChart != null)
            {
                dashboardChart.UpdatePosition(positionX, positionY, width, height);
                ModifiedAt = DateTime.UtcNow;
            }
        }
    }
} 