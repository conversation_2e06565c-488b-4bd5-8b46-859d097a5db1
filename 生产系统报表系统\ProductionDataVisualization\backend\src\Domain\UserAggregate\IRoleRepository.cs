using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Domain.Common;

namespace Domain.UserAggregate
{
    /// <summary>
    /// 角色仓储接口
    /// </summary>
    public interface IRoleRepository : IRepository<Role>
    {
        Task<Role> GetByNameAsync(string name);
        Task<Role?> FindByNameAsync(string name);
        Task<IEnumerable<Role>> GetByUserIdAsync(Guid userId);
        Task<IEnumerable<Role>> GetUserRolesAsync(Guid userId);
    }
} 