using Domain.VisualizationAggregate;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Infrastructure.Persistence.Configurations
{
    /// <summary>
    /// 图表-数据类别关联实体配置
    /// </summary>
    public class ChartDataCategoryConfiguration : IEntityTypeConfiguration<ChartDataCategory>
    {
        public void Configure(EntityTypeBuilder<ChartDataCategory> builder)
        {
            builder.ToTable("ChartDataCategories");

            builder.HasKey(cdc => cdc.Id);

            builder.Property(cdc => cdc.ChartId)
                .IsRequired();

            builder.Property(cdc => cdc.DataCategoryId)
                .IsRequired();

            builder.Property(cdc => cdc.CreatedAt)
                .IsRequired();

            builder.Property(cdc => cdc.ModifiedAt);

            builder.Property(cdc => cdc.CreatedBy)
                .HasMaxLength(50);

            builder.Property(cdc => cdc.ModifiedBy)
                .HasMaxLength(50);

            // 关系
            builder.HasOne(cdc => cdc.Chart)
                .WithMany(c => c.ChartDataCategories)
                .HasForeignKey(cdc => cdc.ChartId)
                .OnDelete(DeleteBehavior.Cascade);

            builder.HasOne(cdc => cdc.DataCategory)
                .WithMany()
                .HasForeignKey(cdc => cdc.DataCategoryId)
                .OnDelete(DeleteBehavior.Restrict);

            // 索引
            builder.HasIndex(cdc => new { cdc.ChartId, cdc.DataCategoryId })
                .IsUnique();
        }
    }
} 