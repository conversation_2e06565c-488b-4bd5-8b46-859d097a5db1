<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>JWT-Decode测试</title>
  <!-- 使用jwt-decode的UMD版本 -->
  <script src="https://unpkg.com/jwt-decode@4.0.0/build/jwt-decode.umd.js"></script>
  <style>
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
      margin: 0;
      padding: 20px;
      background-color: #f0f2f5;
    }
    .container {
      max-width: 800px;
      margin: 0 auto;
      background-color: white;
      padding: 20px;
      border-radius: 8px;
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    }
    button {
      background-color: #1890ff;
      color: white;
      border: none;
      padding: 10px 20px;
      border-radius: 4px;
      cursor: pointer;
      font-size: 16px;
      margin-top: 20px;
    }
    button:hover {
      background-color: #40a9ff;
    }
    .success {
      color: green;
      padding: 10px;
      background-color: #f6ffed;
      border: 1px solid #b7eb8f;
      border-radius: 4px;
      margin: 10px 0;
    }
    .error {
      color: red;
      padding: 10px;
      background-color: #fff2f0;
      border: 1px solid #ffccc7;
      border-radius: 4px;
      margin: 10px 0;
    }
    pre {
      background-color: #f5f5f5;
      padding: 10px;
      border-radius: 4px;
      overflow: auto;
      max-height: 300px;
    }
  </style>
</head>
<body>
  <div class="container">
    <h1>JWT-Decode测试页面</h1>
    <p>这个页面用于测试jwt-decode库的功能，不依赖React。</p>
    
    <div>
      <h2>测试用例1: 基本解码</h2>
      <button id="test1">运行测试1</button>
      <div id="result1"></div>
    </div>
    
    <div>
      <h2>测试用例2: 错误处理</h2>
      <button id="test2">运行测试2</button>
      <div id="result2"></div>
    </div>
    
    <div>
      <h2>测试用例3: localStorage中的token</h2>
      <button id="test3">运行测试3</button>
      <div id="result3"></div>
    </div>
  </div>

  <script>
    // 确保jwt-decode可用
    if (typeof jwtDecode !== 'function') {
      console.error('jwt-decode库未加载成功');
      document.body.innerHTML = '<div class="error">jwt-decode库未加载成功，请检查网络连接</div>';
    }
    
    // 测试用例1: 基本解码
    document.getElementById('test1').addEventListener('click', function() {
      const resultDiv = document.getElementById('result1');
      
      try {
        const testToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkFkbWluIFVzZXIiLCJpYXQiOjE1MTYyMzkwMjIsImV4cCI6MTkxNjIzOTAyMn0.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c';
        
        // 使用v4版本的jwtDecode函数
        const decoded = jwtDecode(testToken);
        
        resultDiv.innerHTML = `
          <div class="success">
            <p>解码成功!</p>
            <pre>${JSON.stringify(decoded, null, 2)}</pre>
          </div>
        `;
      } catch (error) {
        resultDiv.innerHTML = `
          <div class="error">
            <p>解码失败: ${error.message}</p>
            <pre>${error.stack}</pre>
          </div>
        `;
      }
    });
    
    // 测试用例2: 错误处理
    document.getElementById('test2').addEventListener('click', function() {
      const resultDiv = document.getElementById('result2');
      
      try {
        // 使用无效的token
        const invalidToken = 'invalid-token';
        
        // 尝试解码
        const decoded = jwtDecode(invalidToken);
        
        resultDiv.innerHTML = `
          <div class="success">
            <p>解码成功 (这不应该发生)!</p>
            <pre>${JSON.stringify(decoded, null, 2)}</pre>
          </div>
        `;
      } catch (error) {
        resultDiv.innerHTML = `
          <div class="error">
            <p>预期的错误 (这是正常的): ${error.message}</p>
            <pre>${error.stack}</pre>
          </div>
        `;
      }
    });
    
    // 测试用例3: localStorage中的token
    document.getElementById('test3').addEventListener('click', function() {
      const resultDiv = document.getElementById('result3');
      
      try {
        // 从localStorage获取token
        const token = localStorage.getItem('token');
        
        if (!token) {
          resultDiv.innerHTML = `
            <div class="error">
              <p>localStorage中没有token</p>
            </div>
          `;
          return;
        }
        
        // 尝试解码
        const decoded = jwtDecode(token);
        
        // 检查token是否过期
        const currentTime = Date.now() / 1000;
        const isExpired = decoded.exp < currentTime;
        
        resultDiv.innerHTML = `
          <div class="${isExpired ? 'error' : 'success'}">
            <p>${isExpired ? 'Token已过期' : 'Token有效'}</p>
            <p>过期时间: ${new Date(decoded.exp * 1000).toLocaleString()}</p>
            <p>当前时间: ${new Date().toLocaleString()}</p>
            <pre>${JSON.stringify(decoded, null, 2)}</pre>
          </div>
        `;
      } catch (error) {
        resultDiv.innerHTML = `
          <div class="error">
            <p>解码失败: ${error.message}</p>
            <pre>${error.stack}</pre>
          </div>
        `;
      }
    });
  </script>
</body>
</html> 