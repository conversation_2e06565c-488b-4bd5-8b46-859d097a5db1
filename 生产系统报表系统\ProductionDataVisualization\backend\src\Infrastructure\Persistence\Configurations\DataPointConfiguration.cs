using Domain.DataAggregate;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Infrastructure.Persistence.Configurations
{
    /// <summary>
    /// 数据点实体配置
    /// </summary>
    public class DataPointConfiguration : IEntityTypeConfiguration<DataPoint>
    {
        public void Configure(EntityTypeBuilder<DataPoint> builder)
        {
            builder.ToTable("DataPoints");

            builder.HasKey(dp => dp.Id);

            builder.Property(dp => dp.DataSourceId)
                .IsRequired();

            builder.Property(dp => dp.DataCategoryId)
                .IsRequired();

            builder.Property(dp => dp.Value)
                .IsRequired();

            builder.Property(dp => dp.Timestamp)
                .IsRequired();

            builder.Property(dp => dp.Label)
                .HasMaxLength(100);

            builder.Property(dp => dp.IsAbnormal)
                .IsRequired();

            builder.Property(dp => dp.AbnormalReason)
                .HasMaxLength(200);

            builder.Property(dp => dp.CreatedAt)
                .IsRequired();

            builder.Property(dp => dp.ModifiedAt);

            builder.Property(dp => dp.CreatedBy)
                .HasMaxLength(50);

            builder.Property(dp => dp.ModifiedBy)
                .HasMaxLength(50);

            // 关系
            builder.HasOne(dp => dp.DataSource)
                .WithMany(ds => ds.DataPoints)
                .HasForeignKey(dp => dp.DataSourceId)
                .OnDelete(DeleteBehavior.Cascade);

            builder.HasOne(dp => dp.DataCategory)
                .WithMany(dc => dc.DataPoints)
                .HasForeignKey(dp => dp.DataCategoryId)
                .OnDelete(DeleteBehavior.Restrict);

            // 索引
            builder.HasIndex(dp => dp.DataSourceId);
            builder.HasIndex(dp => dp.DataCategoryId);
            builder.HasIndex(dp => dp.Timestamp);
            builder.HasIndex(dp => dp.IsAbnormal);
        }
    }
} 