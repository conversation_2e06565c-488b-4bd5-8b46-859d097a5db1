using System;
using System.Collections.Generic;
using Domain.Common;

namespace Domain.DataAggregate
{
    /// <summary>
    /// 数据类别实体
    /// </summary>
    public class DataCategory : Entity, IAggregateRoot
    {
        public string Name { get; private set; }
        public string Description { get; private set; }
        public string Unit { get; private set; }
        public bool IsActive { get; private set; }
        public Guid DataSourceId { get; private set; }
        public DataSource DataSource { get; private set; }

        private readonly List<Threshold> _thresholds = new();
        public IReadOnlyCollection<Threshold> Thresholds => _thresholds.AsReadOnly();

        private readonly List<DataPoint> _dataPoints = new();
        public IReadOnlyCollection<DataPoint> DataPoints => _dataPoints.AsReadOnly();

        // 防止无参构造函数被外部调用
        private DataCategory() { }

        public DataCategory(string name, string description, string unit, DataSource dataSource)
        {
            if (string.IsNullOrWhiteSpace(name))
                throw new ArgumentException("数据类别名称不能为空", nameof(name));

            if (dataSource == null)
                throw new ArgumentNullException(nameof(dataSource));

            Name = name;
            Description = description ?? string.Empty;
            Unit = unit ?? string.Empty;
            IsActive = true;
            DataSource = dataSource;
            DataSourceId = dataSource.Id;
        }

        public void Update(string name, string description, string unit)
        {
            if (!string.IsNullOrWhiteSpace(name))
                Name = name;

            if (description != null)
                Description = description;

            if (unit != null)
                Unit = unit;

            ModifiedAt = DateTime.UtcNow;
        }

        public void SetActive(bool isActive)
        {
            IsActive = isActive;
            ModifiedAt = DateTime.UtcNow;
        }

        public void AddThreshold(Threshold threshold)
        {
            if (threshold == null)
                throw new ArgumentNullException(nameof(threshold));

            _thresholds.Add(threshold);
            ModifiedAt = DateTime.UtcNow;
        }

        public void RemoveThreshold(Threshold threshold)
        {
            if (threshold == null)
                throw new ArgumentNullException(nameof(threshold));

            _thresholds.Remove(threshold);
            ModifiedAt = DateTime.UtcNow;
        }
    }
} 