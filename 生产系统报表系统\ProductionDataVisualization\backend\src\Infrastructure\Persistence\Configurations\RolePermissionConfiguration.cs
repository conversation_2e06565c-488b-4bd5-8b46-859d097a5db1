using Domain.UserAggregate;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Infrastructure.Persistence.Configurations
{
    /// <summary>
    /// 角色-权限关联实体配置
    /// </summary>
    public class RolePermissionConfiguration : IEntityTypeConfiguration<RolePermission>
    {
        public void Configure(EntityTypeBuilder<RolePermission> builder)
        {
            builder.HasKey(rp => rp.Id);

            builder.Property(rp => rp.RoleId)
                .IsRequired();

            builder.Property(rp => rp.PermissionId)
                .IsRequired();

            builder.Property(rp => rp.CreatedAt)
                .IsRequired();

            builder.Property(rp => rp.ModifiedAt)
                .IsRequired(false);

            // 关系
            builder.HasOne(rp => rp.Role)
                .WithMany(r => r.RolePermissions)
                .HasForeignKey(rp => rp.RoleId)
                .OnDelete(DeleteBehavior.Cascade);

            builder.HasOne(rp => rp.Permission)
                .WithMany(p => p.RolePermissions)
                .HasForeignKey(rp => rp.PermissionId)
                .OnDelete(DeleteBehavior.Cascade);

            // 索引
            builder.HasIndex(rp => new { rp.RoleId, rp.PermissionId }).IsUnique();

            // 预定义角色-权限关系
            builder.HasData(
                // 管理员拥有所有权限
                new { Id = Guid.NewGuid(), RoleId = Role.AdminRoleId, PermissionId = Permission.ManageUsersPermissionId, CreatedAt = DateTime.UtcNow },
                new { Id = Guid.NewGuid(), RoleId = Role.AdminRoleId, PermissionId = Permission.ImportDataPermissionId, CreatedAt = DateTime.UtcNow },
                new { Id = Guid.NewGuid(), RoleId = Role.AdminRoleId, PermissionId = Permission.ViewDashboardsPermissionId, CreatedAt = DateTime.UtcNow },
                new { Id = Guid.NewGuid(), RoleId = Role.AdminRoleId, PermissionId = Permission.ManageChartsPermissionId, CreatedAt = DateTime.UtcNow },
                new { Id = Guid.NewGuid(), RoleId = Role.AdminRoleId, PermissionId = Permission.ManageSystemPermissionId, CreatedAt = DateTime.UtcNow },
                
                // 数据导入员拥有导入数据和查看仪表盘权限
                new { Id = Guid.NewGuid(), RoleId = Role.DataImporterRoleId, PermissionId = Permission.ImportDataPermissionId, CreatedAt = DateTime.UtcNow },
                new { Id = Guid.NewGuid(), RoleId = Role.DataImporterRoleId, PermissionId = Permission.ViewDashboardsPermissionId, CreatedAt = DateTime.UtcNow },
                
                // 查看者只有查看仪表盘权限
                new { Id = Guid.NewGuid(), RoleId = Role.ViewerRoleId, PermissionId = Permission.ViewDashboardsPermissionId, CreatedAt = DateTime.UtcNow }
            );
        }
    }
} 